# 🤖 Cute 3D Robot Character

An interactive 3D robot character built with React, Three.js, and GSAP animations. The robot features a charming, low-poly design with articulating limbs and expressive animations.

## ✨ Features

- **Stylized 3D Design**: Low-poly aesthetic with geometric shapes (boxes, spheres, cylinders)
- **Articulating Limbs**: Fully movable arms and legs with realistic joint movement
- **Expressive Face**: LED-style glowing eyes and sci-fi antenna
- **Rich Animations**: 6 different emotes powered by GSAP
- **Interactive Controls**: Easy-to-use button interface
- **Responsive Design**: Works on desktop and mobile devices
- **Performance Optimized**: Efficient rendering for smooth web performance

## 🎭 Available Animations

1. **👋 Wave**: Friendly arm waving gesture
2. **🦘 Jump**: Full-body jumping animation with realistic physics
3. **👊 Punch**: Dynamic punching motion with wind-up
4. **✅ Nod Yes**: Head nodding for affirmative responses
5. **❌ Shake No**: Head shaking for negative responses
6. **💃 Dance**: Rhythmic dancing with body movement

## 🛠️ Technology Stack

- **React 19**: Modern React with hooks and functional components
- **Three.js**: 3D graphics rendering
- **@react-three/fiber**: React renderer for Three.js
- **@react-three/drei**: Useful helpers and abstractions
- **GSAP**: High-performance animations
- **Vite**: Fast development and build tool

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- Yarn package manager

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd 3danimations
```

2. Install dependencies:
```bash
yarn install
```

3. Start the development server:
```bash
yarn dev
```

4. Open your browser and navigate to `http://localhost:5173`

## 🎮 Usage

### Basic Controls

- **Animation Buttons**: Click any animation button to make the robot perform that action
- **Random Animation**: Use the "🎲 Random Animation" button for surprise movements
- **Camera Controls**:
  - Mouse drag to rotate the camera
  - Mouse wheel to zoom in/out
  - Right-click drag to pan

### Robot Features

- **Idle Animation**: The robot breathes subtly when not performing actions
- **Glowing Elements**: Eyes, chest panel, and antenna have emissive materials
- **Realistic Lighting**: Multiple light sources create depth and atmosphere
- **Contact Shadows**: Ground shadows enhance the sense of presence

## 📁 Project Structure

```
src/
├── components/
│   ├── Robot3D/
│   │   ├── Robot3D.jsx          # Main robot component
│   │   ├── RobotGeometry.jsx    # 3D geometry and materials
│   │   ├── RobotAnimations.js   # GSAP animation functions
│   │   ├── RobotControls.jsx    # UI control panel
│   │   └── RobotControls.css    # Control panel styles
│   └── Scene3D.jsx              # Three.js scene setup
├── hooks/
│   └── useRobotAnimations.js    # Animation state management
├── App.jsx                      # Main application component
├── App.css                      # Application styles
└── main.jsx                     # Application entry point
```

## 🎨 Customization

### Colors and Materials

The robot's appearance can be customized by modifying the materials in `RobotGeometry.jsx`:

```javascript
const robotMaterial = new THREE.MeshStandardMaterial({
  color: '#4A90E2',        // Main body color
  metalness: 0.3,
  roughness: 0.4,
});

const accentMaterial = new THREE.MeshStandardMaterial({
  color: '#FF6B35',        // Accent color (hands, feet, antenna)
  metalness: 0.5,
  roughness: 0.3,
});
```

### Adding New Animations

1. Add the animation function to `RobotAnimations.js`
2. Update the `playAnimation` method in `Robot3D.jsx`
3. Add the new animation to the controls in `RobotControls.jsx`

### Performance Tuning

- Adjust geometry detail in `RobotGeometry.jsx` (sphere/cylinder segments)
- Modify shadow quality in `Scene3D.jsx`
- Adjust animation frame rates in GSAP timelines

## 🔧 Development

### Available Scripts

- `yarn dev`: Start development server
- `yarn build`: Build for production
- `yarn preview`: Preview production build
- `yarn lint`: Run ESLint

### Browser Support

- Chrome 88+
- Firefox 78+
- Safari 14+
- Edge 88+

## 📱 Mobile Support

The robot is fully responsive and includes:
- Touch-friendly controls
- Optimized layout for small screens
- Reduced animation complexity on mobile devices

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- Three.js community for excellent 3D web graphics
- GSAP for powerful animation capabilities
- React Three Fiber for seamless React integration

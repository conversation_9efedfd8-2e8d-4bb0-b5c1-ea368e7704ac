import { useState, useCallback, useRef } from 'react';

export function useRobotAnimations() {
  const [currentAnimation, setCurrentAnimation] = useState('idle');
  const [isAnimating, setIsAnimating] = useState(false);
  const robotRef = useRef();

  const handleAnimationChange = useCallback((animationType) => {
    setCurrentAnimation(animationType);
    setIsAnimating(true);
    
    // Reset to idle after animation completes
    if (animationType !== 'idle' && animationType !== 'reset') {
      // Estimate animation duration and reset to idle
      const animationDurations = {
        wave: 1500,
        jump: 1500,
        punch: 800,
        nodYes: 1200,
        shakeNo: 1200,
        dance: 2700
      };
      
      const duration = animationDurations[animationType] || 1000;
      
      setTimeout(() => {
        setCurrentAnimation('idle');
        setIsAnimating(false);
      }, duration);
    } else {
      setIsAnimating(false);
    }
  }, []);

  const playAnimation = useCallback((animationType) => {
    if (robotRef.current && !isAnimating) {
      robotRef.current.playAnimation(animationType);
    }
  }, [isAnimating]);

  const playRandomAnimation = useCallback(() => {
    if (isAnimating) return;
    
    const animations = ['wave', 'jump', 'punch', 'nodYes', 'shakeNo', 'dance'];
    const randomAnimation = animations[Math.floor(Math.random() * animations.length)];
    playAnimation(randomAnimation);
  }, [isAnimating, playAnimation]);

  return {
    robotRef,
    currentAnimation,
    isAnimating,
    handleAnimationChange,
    playAnimation,
    playRandomAnimation
  };
}

import React, { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';

// Robot component with articulated parts
export function RobotGeometry({ animationRefs, ...props }) {
  const groupRef = useRef();

  // Create refs for each body part
  const headRef = useRef();
  const bodyRef = useRef();
  const leftArmRef = useRef();
  const rightArmRef = useRef();
  const leftLegRef = useRef();
  const rightLegRef = useRef();
  const leftEyeRef = useRef();
  const rightEyeRef = useRef();
  const antennaRef = useRef();
  const chestPanelRef = useRef();

  // Pass refs to parent for animations
  React.useEffect(() => {
    if (animationRefs) {
      animationRefs.current = {
        group: groupRef,
        head: headRef,
        body: bodyRef,
        leftArm: leftArmRef,
        rightArm: rightArmRef,
        leftLeg: leftLegRef,
        rightLeg: rightLegRef,
        leftEye: leftEyeRef,
        rightEye: rightEyeRef,
        antenna: antennaRef,
        chestPanel: chestPanelRef,
      };
    }
  }, [animationRefs]);

  // Groot-like Materials - Organic and tree-like
  const barkMaterial = useMemo(() => new THREE.MeshStandardMaterial({
    color: '#8B4513', // Saddle brown
    roughness: 0.9,
    metalness: 0.0,
    normalScale: new THREE.Vector2(1, 1),
  }), []);

  const darkBarkMaterial = useMemo(() => new THREE.MeshStandardMaterial({
    color: '#654321', // Dark brown
    roughness: 0.95,
    metalness: 0.0,
  }), []);

  const leafMaterial = useMemo(() => new THREE.MeshStandardMaterial({
    color: '#228B22', // Forest green
    roughness: 0.8,
    metalness: 0.0,
  }), []);

  const eyeMaterial = useMemo(() => new THREE.MeshStandardMaterial({
    color: '#FFFF88', // Warm yellow
    emissive: '#FFFF88',
    emissiveIntensity: 0.3,
    metalness: 0.0,
    roughness: 0.2,
  }), []);

  const glowMaterial = useMemo(() => new THREE.MeshStandardMaterial({
    color: '#90EE90', // Light green
    emissive: '#90EE90',
    emissiveIntensity: 0.4,
    metalness: 0.0,
    roughness: 0.3,
  }), []);

  const mouthMaterial = useMemo(() => new THREE.MeshStandardMaterial({
    color: '#2F1B14', // Very dark brown
    roughness: 0.9,
    metalness: 0.0,
  }), []);

  // Idle breathing animation and eye blinking
  useFrame((state) => {
    if (bodyRef.current) {
      bodyRef.current.scale.y = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.02;
    }
    if (chestPanelRef.current) {
      chestPanelRef.current.material.emissiveIntensity = 0.4 + Math.sin(state.clock.elapsedTime * 3) * 0.2;
    }
    // Eye blinking effect
    if (leftEyeRef.current && rightEyeRef.current) {
      const blinkTime = state.clock.elapsedTime * 0.5;
      const blink = Math.sin(blinkTime) > 0.95 ? 0.3 : 0.6;
      leftEyeRef.current.material.emissiveIntensity = blink;
      rightEyeRef.current.material.emissiveIntensity = blink;
    }
    // Antenna glow
    if (antennaRef.current) {
      antennaRef.current.material.emissiveIntensity = 0.1 + Math.sin(state.clock.elapsedTime * 4) * 0.1;
    }
  });

  return (
    <group ref={groupRef} {...props}>
      {/* Body */}
      <mesh ref={bodyRef} position={[0, 0, 0]} castShadow receiveShadow>
        <boxGeometry args={[1.2, 1.5, 0.8]} />
        <primitive object={robotMaterial} />
      </mesh>

      {/* Body details */}
      <mesh position={[0, -0.6, 0.41]} castShadow>
        <boxGeometry args={[0.8, 0.2, 0.02]} />
        <primitive object={accentMaterial} />
      </mesh>

      {/* Chest Panel */}
      <mesh ref={chestPanelRef} position={[0, 0.2, 0.41]} castShadow>
        <boxGeometry args={[0.6, 0.4, 0.02]} />
        <primitive object={chestMaterial} />
      </mesh>

      {/* Chest details */}
      <mesh position={[-0.15, 0.2, 0.42]} castShadow>
        <sphereGeometry args={[0.03, 8, 8]} />
        <primitive object={eyeMaterial} />
      </mesh>
      <mesh position={[0.15, 0.2, 0.42]} castShadow>
        <sphereGeometry args={[0.03, 8, 8]} />
        <primitive object={eyeMaterial} />
      </mesh>

      {/* Head */}
      <group ref={headRef} position={[0, 1.2, 0]}>
        <mesh castShadow receiveShadow>
          <boxGeometry args={[0.9, 0.8, 0.8]} />
          <primitive object={robotMaterial} />
        </mesh>

        {/* Head visor */}
        <mesh position={[0, 0.1, 0.41]} castShadow>
          <boxGeometry args={[0.7, 0.3, 0.02]} />
          <primitive object={jointMaterial} />
        </mesh>

        {/* Eyes */}
        <mesh ref={leftEyeRef} position={[-0.2, 0.1, 0.42]} castShadow>
          <sphereGeometry args={[0.08, 12, 12]} />
          <primitive object={eyeMaterial} />
        </mesh>
        <mesh ref={rightEyeRef} position={[0.2, 0.1, 0.42]} castShadow>
          <sphereGeometry args={[0.08, 12, 12]} />
          <primitive object={eyeMaterial} />
        </mesh>

        {/* Mouth */}
        <mesh position={[0, -0.1, 0.41]} castShadow>
          <boxGeometry args={[0.3, 0.05, 0.02]} />
          <primitive object={jointMaterial} />
        </mesh>

        {/* Antenna */}
        <mesh ref={antennaRef} position={[0, 0.5, 0]} castShadow>
          <cylinderGeometry args={[0.02, 0.02, 0.3]} />
          <primitive object={accentMaterial} />
        </mesh>
        <mesh position={[0, 0.75, 0]} castShadow>
          <sphereGeometry args={[0.05, 12, 12]} />
          <primitive object={eyeMaterial} />
        </mesh>
      </group>

      {/* Left Arm */}
      <group ref={leftArmRef} position={[-0.8, 0.3, 0]}>
        {/* Shoulder joint */}
        <mesh position={[0, 0.2, 0]} castShadow>
          <sphereGeometry args={[0.12, 12, 12]} />
          <primitive object={jointMaterial} />
        </mesh>
        {/* Upper arm */}
        <mesh position={[0, 0, 0]} castShadow receiveShadow>
          <cylinderGeometry args={[0.15, 0.15, 0.6]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Elbow joint */}
        <mesh position={[0, -0.35, 0]} castShadow>
          <sphereGeometry args={[0.1, 12, 12]} />
          <primitive object={jointMaterial} />
        </mesh>
        {/* Lower arm */}
        <mesh position={[0, -0.5, 0]} castShadow receiveShadow>
          <cylinderGeometry args={[0.12, 0.12, 0.5]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Wrist */}
        <mesh position={[0, -0.8, 0]} castShadow>
          <cylinderGeometry args={[0.08, 0.08, 0.1]} />
          <primitive object={jointMaterial} />
        </mesh>
        {/* Hand */}
        <mesh position={[0, -0.9, 0]} castShadow>
          <sphereGeometry args={[0.15, 12, 12]} />
          <primitive object={accentMaterial} />
        </mesh>
      </group>

      {/* Right Arm */}
      <group ref={rightArmRef} position={[0.8, 0.3, 0]}>
        {/* Shoulder joint */}
        <mesh position={[0, 0.2, 0]} castShadow>
          <sphereGeometry args={[0.12, 12, 12]} />
          <primitive object={jointMaterial} />
        </mesh>
        {/* Upper arm */}
        <mesh position={[0, 0, 0]} castShadow receiveShadow>
          <cylinderGeometry args={[0.15, 0.15, 0.6]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Elbow joint */}
        <mesh position={[0, -0.35, 0]} castShadow>
          <sphereGeometry args={[0.1, 12, 12]} />
          <primitive object={jointMaterial} />
        </mesh>
        {/* Lower arm */}
        <mesh position={[0, -0.5, 0]} castShadow receiveShadow>
          <cylinderGeometry args={[0.12, 0.12, 0.5]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Wrist */}
        <mesh position={[0, -0.8, 0]} castShadow>
          <cylinderGeometry args={[0.08, 0.08, 0.1]} />
          <primitive object={jointMaterial} />
        </mesh>
        {/* Hand */}
        <mesh position={[0, -0.9, 0]} castShadow>
          <sphereGeometry args={[0.15, 12, 12]} />
          <primitive object={accentMaterial} />
        </mesh>
      </group>

      {/* Left Leg */}
      <group ref={leftLegRef} position={[-0.3, -1.2, 0]}>
        {/* Hip joint */}
        <mesh position={[0, 0.3, 0]} castShadow>
          <sphereGeometry args={[0.15, 12, 12]} />
          <primitive object={jointMaterial} />
        </mesh>
        {/* Upper leg */}
        <mesh position={[0, 0, 0]} castShadow receiveShadow>
          <cylinderGeometry args={[0.18, 0.18, 0.8]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Knee joint */}
        <mesh position={[0, -0.45, 0]} castShadow>
          <sphereGeometry args={[0.12, 12, 12]} />
          <primitive object={jointMaterial} />
        </mesh>
        {/* Lower leg */}
        <mesh position={[0, -0.6, 0]} castShadow receiveShadow>
          <cylinderGeometry args={[0.15, 0.15, 0.6]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Ankle */}
        <mesh position={[0, -0.95, 0]} castShadow>
          <cylinderGeometry args={[0.1, 0.1, 0.1]} />
          <primitive object={jointMaterial} />
        </mesh>
        {/* Foot */}
        <mesh position={[0, -1.1, 0.1]} castShadow receiveShadow>
          <boxGeometry args={[0.3, 0.2, 0.5]} />
          <primitive object={accentMaterial} />
        </mesh>
      </group>

      {/* Right Leg */}
      <group ref={rightLegRef} position={[0.3, -1.2, 0]}>
        {/* Hip joint */}
        <mesh position={[0, 0.3, 0]} castShadow>
          <sphereGeometry args={[0.15, 12, 12]} />
          <primitive object={jointMaterial} />
        </mesh>
        {/* Upper leg */}
        <mesh position={[0, 0, 0]} castShadow receiveShadow>
          <cylinderGeometry args={[0.18, 0.18, 0.8]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Knee joint */}
        <mesh position={[0, -0.45, 0]} castShadow>
          <sphereGeometry args={[0.12, 12, 12]} />
          <primitive object={jointMaterial} />
        </mesh>
        {/* Lower leg */}
        <mesh position={[0, -0.6, 0]} castShadow receiveShadow>
          <cylinderGeometry args={[0.15, 0.15, 0.6]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Ankle */}
        <mesh position={[0, -0.95, 0]} castShadow>
          <cylinderGeometry args={[0.1, 0.1, 0.1]} />
          <primitive object={jointMaterial} />
        </mesh>
        {/* Foot */}
        <mesh position={[0, -1.1, 0.1]} castShadow receiveShadow>
          <boxGeometry args={[0.3, 0.2, 0.5]} />
          <primitive object={accentMaterial} />
        </mesh>
      </group>
    </group>
  );
}

import React, { useRef, useMemo } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';

// Robot component with articulated parts
export function RobotGeometry({ animationRefs, ...props }) {
  const groupRef = useRef();
  
  // Create refs for each body part
  const headRef = useRef();
  const bodyRef = useRef();
  const leftArmRef = useRef();
  const rightArmRef = useRef();
  const leftLegRef = useRef();
  const rightLegRef = useRef();
  const leftEyeRef = useRef();
  const rightEyeRef = useRef();
  const antennaRef = useRef();
  const chestPanelRef = useRef();

  // Pass refs to parent for animations
  React.useEffect(() => {
    if (animationRefs) {
      animationRefs.current = {
        group: groupRef,
        head: headRef,
        body: bodyRef,
        leftArm: leftArmRef,
        rightArm: rightArmRef,
        leftLeg: leftLegRef,
        rightLeg: rightLegRef,
        leftEye: leftEyeRef,
        rightEye: rightEyeRef,
        antenna: antennaRef,
        chestPanel: chestPanelRef,
      };
    }
  }, [animationRefs]);

  // Materials
  const robotMaterial = useMemo(() => new THREE.MeshStandardMaterial({ 
    color: '#4A90E2',
    metalness: 0.3,
    roughness: 0.4,
  }), []);

  const accentMaterial = useMemo(() => new THREE.MeshStandardMaterial({ 
    color: '#FF6B35',
    metalness: 0.5,
    roughness: 0.3,
  }), []);

  const eyeMaterial = useMemo(() => new THREE.MeshStandardMaterial({ 
    color: '#00FF88',
    emissive: '#00FF88',
    emissiveIntensity: 0.3,
  }), []);

  const chestMaterial = useMemo(() => new THREE.MeshStandardMaterial({ 
    color: '#00AAFF',
    emissive: '#00AAFF',
    emissiveIntensity: 0.2,
  }), []);

  // Idle breathing animation
  useFrame((state) => {
    if (bodyRef.current) {
      bodyRef.current.scale.y = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.02;
    }
    if (chestPanelRef.current) {
      chestPanelRef.current.material.emissiveIntensity = 0.2 + Math.sin(state.clock.elapsedTime * 3) * 0.1;
    }
  });

  return (
    <group ref={groupRef} {...props}>
      {/* Body */}
      <mesh ref={bodyRef} position={[0, 0, 0]}>
        <boxGeometry args={[1.2, 1.5, 0.8]} />
        <primitive object={robotMaterial} />
      </mesh>

      {/* Chest Panel */}
      <mesh ref={chestPanelRef} position={[0, 0.2, 0.41]}>
        <boxGeometry args={[0.6, 0.4, 0.02]} />
        <primitive object={chestMaterial} />
      </mesh>

      {/* Head */}
      <group ref={headRef} position={[0, 1.2, 0]}>
        <mesh>
          <boxGeometry args={[0.8, 0.8, 0.8]} />
          <primitive object={robotMaterial} />
        </mesh>
        
        {/* Eyes */}
        <mesh ref={leftEyeRef} position={[-0.2, 0.1, 0.41]}>
          <sphereGeometry args={[0.08, 8, 8]} />
          <primitive object={eyeMaterial} />
        </mesh>
        <mesh ref={rightEyeRef} position={[0.2, 0.1, 0.41]}>
          <sphereGeometry args={[0.08, 8, 8]} />
          <primitive object={eyeMaterial} />
        </mesh>

        {/* Antenna */}
        <mesh ref={antennaRef} position={[0, 0.5, 0]}>
          <cylinderGeometry args={[0.02, 0.02, 0.3]} />
          <primitive object={accentMaterial} />
        </mesh>
        <mesh position={[0, 0.75, 0]}>
          <sphereGeometry args={[0.05, 8, 8]} />
          <primitive object={eyeMaterial} />
        </mesh>
      </group>

      {/* Left Arm */}
      <group ref={leftArmRef} position={[-0.8, 0.3, 0]}>
        {/* Upper arm */}
        <mesh position={[0, 0, 0]}>
          <cylinderGeometry args={[0.15, 0.15, 0.6]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Lower arm */}
        <mesh position={[0, -0.5, 0]}>
          <cylinderGeometry args={[0.12, 0.12, 0.5]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Hand */}
        <mesh position={[0, -0.9, 0]}>
          <sphereGeometry args={[0.15, 8, 8]} />
          <primitive object={accentMaterial} />
        </mesh>
      </group>

      {/* Right Arm */}
      <group ref={rightArmRef} position={[0.8, 0.3, 0]}>
        {/* Upper arm */}
        <mesh position={[0, 0, 0]}>
          <cylinderGeometry args={[0.15, 0.15, 0.6]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Lower arm */}
        <mesh position={[0, -0.5, 0]}>
          <cylinderGeometry args={[0.12, 0.12, 0.5]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Hand */}
        <mesh position={[0, -0.9, 0]}>
          <sphereGeometry args={[0.15, 8, 8]} />
          <primitive object={accentMaterial} />
        </mesh>
      </group>

      {/* Left Leg */}
      <group ref={leftLegRef} position={[-0.3, -1.2, 0]}>
        {/* Upper leg */}
        <mesh position={[0, 0, 0]}>
          <cylinderGeometry args={[0.18, 0.18, 0.8]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Lower leg */}
        <mesh position={[0, -0.6, 0]}>
          <cylinderGeometry args={[0.15, 0.15, 0.6]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Foot */}
        <mesh position={[0, -1.1, 0.1]}>
          <boxGeometry args={[0.3, 0.2, 0.5]} />
          <primitive object={accentMaterial} />
        </mesh>
      </group>

      {/* Right Leg */}
      <group ref={rightLegRef} position={[0.3, -1.2, 0]}>
        {/* Upper leg */}
        <mesh position={[0, 0, 0]}>
          <cylinderGeometry args={[0.18, 0.18, 0.8]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Lower leg */}
        <mesh position={[0, -0.6, 0]}>
          <cylinderGeometry args={[0.15, 0.15, 0.6]} />
          <primitive object={robotMaterial} />
        </mesh>
        {/* Foot */}
        <mesh position={[0, -1.1, 0.1]}>
          <boxGeometry args={[0.3, 0.2, 0.5]} />
          <primitive object={accentMaterial} />
        </mesh>
      </group>
    </group>
  );
}

import { gsap } from 'gsap';

export class RobotAnimations {
  constructor(refs) {
    this.refs = refs;
    this.isAnimating = false;
    console.log('RobotAnimations initialized with refs:', refs);
  }

  // Wave animation
  wave() {
    console.log('Wave animation called, refs:', this.refs);
    if (this.isAnimating) {
      console.log('Already animating, skipping wave');
      return;
    }

    if (!this.refs.current || !this.refs.current.rightArm || !this.refs.current.rightArm.current) {
      console.error('Right arm ref not available for wave animation');
      return;
    }

    this.isAnimating = true;
    console.log('Starting wave animation');

    const tl = gsap.timeline({
      onComplete: () => {
        this.isAnimating = false;
        console.log('Wave animation completed');
      }
    });

    // Raise arm and wave
    tl.to(this.refs.current.rightArm.current.rotation, {
      z: -Math.PI / 2,
      duration: 0.4,
      ease: "power2.out"
    })
    .to(this.refs.current.rightArm.current.rotation, {
      y: Math.PI / 8,
      duration: 0.25,
      ease: "power2.inOut",
      yoyo: true,
      repeat: 4
    })
    .to(this.refs.current.rightArm.current.rotation, {
      z: 0,
      y: 0,
      duration: 0.5,
      ease: "power2.inOut"
    });

    return tl;
  }

  // Jump animation
  jump() {
    if (this.isAnimating) return;

    if (!this.refs.current || !this.refs.current.group || !this.refs.current.group.current) {
      console.error('Group ref not available for jump animation');
      return;
    }

    this.isAnimating = true;
    console.log('Starting jump animation');

    const tl = gsap.timeline({
      onComplete: () => {
        this.isAnimating = false;
        console.log('Jump animation completed');
      }
    });

    // Crouch down
    tl.to(this.refs.current.group.current.position, {
      y: -0.3,
      duration: 0.2,
      ease: "power2.in"
    })
    .to([this.refs.current.leftLeg.current.rotation, this.refs.current.rightLeg.current.rotation], {
      x: Math.PI / 6,
      duration: 0.2,
      ease: "power2.in"
    }, 0)
    // Jump up
    .to(this.refs.current.group.current.position, {
      y: 1.5,
      duration: 0.4,
      ease: "power2.out"
    })
    .to([this.refs.current.leftLeg.current.rotation, this.refs.current.rightLeg.current.rotation], {
      x: -Math.PI / 8,
      duration: 0.2,
      ease: "power2.out"
    }, "-=0.4")
    .to([this.refs.current.leftArm.current.rotation, this.refs.current.rightArm.current.rotation], {
      x: -Math.PI / 4,
      duration: 0.2,
      ease: "power2.out"
    }, "-=0.4")
    // Fall down
    .to(this.refs.current.group.current.position, {
      y: 0,
      duration: 0.4,
      ease: "power2.in"
    })
    .to([this.refs.current.leftLeg.current.rotation, this.refs.current.rightLeg.current.rotation], {
      x: 0,
      duration: 0.3,
      ease: "bounce.out"
    }, "-=0.2")
    .to([this.refs.current.leftArm.current.rotation, this.refs.current.rightArm.current.rotation], {
      x: 0,
      duration: 0.3,
      ease: "bounce.out"
    }, "-=0.3");

    return tl;
  }

  // Punch animation
  punch() {
    if (this.isAnimating) return;

    if (!this.refs.current || !this.refs.current.leftArm || !this.refs.current.leftArm.current) {
      console.error('Left arm ref not available for punch animation');
      return;
    }

    this.isAnimating = true;
    console.log('Starting punch animation');

    const tl = gsap.timeline({
      onComplete: () => {
        this.isAnimating = false;
        console.log('Punch animation completed');
      }
    });

    // Wind up
    tl.to(this.refs.current.leftArm.current.rotation, {
      x: -Math.PI / 4,
      z: Math.PI / 6,
      duration: 0.3,
      ease: "power2.in"
    })
    .to(this.refs.current.leftArm.current.position, {
      z: -0.3,
      duration: 0.3,
      ease: "power2.in"
    }, 0)
    // Punch forward
    .to(this.refs.current.leftArm.current.rotation, {
      x: 0,
      z: 0,
      duration: 0.2,
      ease: "power2.out"
    })
    .to(this.refs.current.leftArm.current.position, {
      z: 0.5,
      duration: 0.2,
      ease: "power2.out"
    }, "-=0.2")
    // Return to position
    .to(this.refs.current.leftArm.current.position, {
      z: 0,
      duration: 0.4,
      ease: "power2.inOut"
    });

    return tl;
  }

  // Nod yes animation
  nodYes() {
    if (this.isAnimating) return;

    if (!this.refs.current || !this.refs.current.head || !this.refs.current.head.current) {
      console.error('Head ref not available for nod yes animation');
      return;
    }

    this.isAnimating = true;
    console.log('Starting nod yes animation');

    const tl = gsap.timeline({
      onComplete: () => {
        this.isAnimating = false;
        console.log('Nod yes animation completed');
      }
    });

    tl.to(this.refs.current.head.current.rotation, {
      x: Math.PI / 6,
      duration: 0.3,
      ease: "power2.inOut",
      yoyo: true,
      repeat: 3
    });

    return tl;
  }

  // Shake no animation
  shakeNo() {
    if (this.isAnimating) return;

    if (!this.refs.current || !this.refs.current.head || !this.refs.current.head.current) {
      console.error('Head ref not available for shake no animation');
      return;
    }

    this.isAnimating = true;
    console.log('Starting shake no animation');

    const tl = gsap.timeline({
      onComplete: () => {
        this.isAnimating = false;
        console.log('Shake no animation completed');
      }
    });

    tl.to(this.refs.current.head.current.rotation, {
      y: Math.PI / 4,
      duration: 0.2,
      ease: "power2.inOut",
      yoyo: true,
      repeat: 5
    });

    return tl;
  }

  // Dance animation
  dance() {
    if (this.isAnimating) return;

    if (!this.refs.current || !this.refs.current.group || !this.refs.current.group.current) {
      console.error('Group ref not available for dance animation');
      return;
    }

    this.isAnimating = true;
    console.log('Starting dance animation');

    const tl = gsap.timeline({
      onComplete: () => {
        this.isAnimating = false;
        console.log('Dance animation completed');
      },
      repeat: 2
    });

    // Side to side movement
    tl.to(this.refs.current.group.current.rotation, {
      z: Math.PI / 12,
      duration: 0.3,
      ease: "power2.inOut"
    })
    .to([this.refs.current.leftArm.current.rotation, this.refs.current.rightArm.current.rotation], {
      z: Math.PI / 4,
      duration: 0.3,
      ease: "power2.inOut"
    }, 0)
    .to(this.refs.current.group.current.rotation, {
      z: -Math.PI / 12,
      duration: 0.3,
      ease: "power2.inOut"
    })
    .to([this.refs.current.leftArm.current.rotation, this.refs.current.rightArm.current.rotation], {
      z: -Math.PI / 4,
      duration: 0.3,
      ease: "power2.inOut"
    }, "-=0.3")
    .to(this.refs.current.group.current.rotation, {
      z: 0,
      duration: 0.3,
      ease: "power2.inOut"
    })
    .to([this.refs.current.leftArm.current.rotation, this.refs.current.rightArm.current.rotation], {
      z: 0,
      duration: 0.3,
      ease: "power2.inOut"
    }, "-=0.3");

    return tl;
  }

  // Reset all animations
  reset() {
    if (!this.refs.current || !this.refs.current.group) {
      console.error('Refs not available for reset');
      return;
    }

    console.log('Resetting all animations');

    gsap.killTweensOf([
      this.refs.current.group.current,
      this.refs.current.head.current,
      this.refs.current.leftArm.current,
      this.refs.current.rightArm.current,
      this.refs.current.leftLeg.current,
      this.refs.current.rightLeg.current
    ]);

    // Reset all rotations and positions
    gsap.set([
      this.refs.current.group.current.rotation,
      this.refs.current.head.current.rotation,
      this.refs.current.leftArm.current.rotation,
      this.refs.current.rightArm.current.rotation,
      this.refs.current.leftLeg.current.rotation,
      this.refs.current.rightLeg.current.rotation
    ], { x: 0, y: 0, z: 0 });

    gsap.set(this.refs.current.group.current.position, { x: 0, y: 0, z: 0 });
    gsap.set(this.refs.current.leftArm.current.position, { z: 0 });

    this.isAnimating = false;
  }
}

import { gsap } from 'gsap';

export class RobotAnimations {
  constructor(refs) {
    this.refs = refs;
    this.isAnimating = false;
  }

  // Wave animation
  wave() {
    if (this.isAnimating || !this.refs.rightArm) return;
    
    this.isAnimating = true;
    const tl = gsap.timeline({
      onComplete: () => { this.isAnimating = false; }
    });

    // Raise arm and wave
    tl.to(this.refs.rightArm.current.rotation, {
      z: -Math.PI / 3,
      duration: 0.3,
      ease: "power2.out"
    })
    .to(this.refs.rightArm.current.rotation, {
      y: Math.PI / 6,
      duration: 0.2,
      ease: "power2.inOut",
      yoyo: true,
      repeat: 3
    })
    .to(this.refs.rightArm.current.rotation, {
      z: 0,
      y: 0,
      duration: 0.4,
      ease: "power2.inOut"
    });

    return tl;
  }

  // Jump animation
  jump() {
    if (this.isAnimating || !this.refs.group) return;
    
    this.isAnimating = true;
    const tl = gsap.timeline({
      onComplete: () => { this.isAnimating = false; }
    });

    // Crouch down
    tl.to(this.refs.group.current.position, {
      y: -0.3,
      duration: 0.2,
      ease: "power2.in"
    })
    .to([this.refs.leftLeg.current.rotation, this.refs.rightLeg.current.rotation], {
      x: Math.PI / 6,
      duration: 0.2,
      ease: "power2.in"
    }, 0)
    // Jump up
    .to(this.refs.group.current.position, {
      y: 1.5,
      duration: 0.4,
      ease: "power2.out"
    })
    .to([this.refs.leftLeg.current.rotation, this.refs.rightLeg.current.rotation], {
      x: -Math.PI / 8,
      duration: 0.2,
      ease: "power2.out"
    }, "-=0.4")
    .to([this.refs.leftArm.current.rotation, this.refs.rightArm.current.rotation], {
      x: -Math.PI / 4,
      duration: 0.2,
      ease: "power2.out"
    }, "-=0.4")
    // Fall down
    .to(this.refs.group.current.position, {
      y: 0,
      duration: 0.4,
      ease: "power2.in"
    })
    .to([this.refs.leftLeg.current.rotation, this.refs.rightLeg.current.rotation], {
      x: 0,
      duration: 0.3,
      ease: "bounce.out"
    }, "-=0.2")
    .to([this.refs.leftArm.current.rotation, this.refs.rightArm.current.rotation], {
      x: 0,
      duration: 0.3,
      ease: "bounce.out"
    }, "-=0.3");

    return tl;
  }

  // Punch animation
  punch() {
    if (this.isAnimating || !this.refs.leftArm) return;
    
    this.isAnimating = true;
    const tl = gsap.timeline({
      onComplete: () => { this.isAnimating = false; }
    });

    // Wind up
    tl.to(this.refs.leftArm.current.rotation, {
      x: -Math.PI / 4,
      z: Math.PI / 6,
      duration: 0.2,
      ease: "power2.in"
    })
    .to(this.refs.leftArm.current.position, {
      z: -0.3,
      duration: 0.2,
      ease: "power2.in"
    }, 0)
    // Punch forward
    .to(this.refs.leftArm.current.rotation, {
      x: 0,
      z: 0,
      duration: 0.15,
      ease: "power2.out"
    })
    .to(this.refs.leftArm.current.position, {
      z: 0.5,
      duration: 0.15,
      ease: "power2.out"
    }, "-=0.15")
    // Return to position
    .to(this.refs.leftArm.current.position, {
      z: 0,
      duration: 0.3,
      ease: "power2.inOut"
    });

    return tl;
  }

  // Nod yes animation
  nodYes() {
    if (this.isAnimating || !this.refs.head) return;
    
    this.isAnimating = true;
    const tl = gsap.timeline({
      onComplete: () => { this.isAnimating = false; }
    });

    tl.to(this.refs.head.current.rotation, {
      x: Math.PI / 8,
      duration: 0.3,
      ease: "power2.inOut",
      yoyo: true,
      repeat: 3
    });

    return tl;
  }

  // Shake no animation
  shakeNo() {
    if (this.isAnimating || !this.refs.head) return;
    
    this.isAnimating = true;
    const tl = gsap.timeline({
      onComplete: () => { this.isAnimating = false; }
    });

    tl.to(this.refs.head.current.rotation, {
      y: Math.PI / 6,
      duration: 0.2,
      ease: "power2.inOut",
      yoyo: true,
      repeat: 5
    });

    return tl;
  }

  // Dance animation
  dance() {
    if (this.isAnimating || !this.refs.group) return;
    
    this.isAnimating = true;
    const tl = gsap.timeline({
      onComplete: () => { this.isAnimating = false; },
      repeat: 2
    });

    // Side to side movement
    tl.to(this.refs.group.current.rotation, {
      z: Math.PI / 12,
      duration: 0.3,
      ease: "power2.inOut"
    })
    .to([this.refs.leftArm.current.rotation, this.refs.rightArm.current.rotation], {
      z: Math.PI / 4,
      duration: 0.3,
      ease: "power2.inOut"
    }, 0)
    .to(this.refs.group.current.rotation, {
      z: -Math.PI / 12,
      duration: 0.3,
      ease: "power2.inOut"
    })
    .to([this.refs.leftArm.current.rotation, this.refs.rightArm.current.rotation], {
      z: -Math.PI / 4,
      duration: 0.3,
      ease: "power2.inOut"
    }, "-=0.3")
    .to(this.refs.group.current.rotation, {
      z: 0,
      duration: 0.3,
      ease: "power2.inOut"
    })
    .to([this.refs.leftArm.current.rotation, this.refs.rightArm.current.rotation], {
      z: 0,
      duration: 0.3,
      ease: "power2.inOut"
    }, "-=0.3");

    return tl;
  }

  // Reset all animations
  reset() {
    if (!this.refs.group) return;
    
    gsap.killTweensOf([
      this.refs.group.current,
      this.refs.head.current,
      this.refs.leftArm.current,
      this.refs.rightArm.current,
      this.refs.leftLeg.current,
      this.refs.rightLeg.current
    ]);

    // Reset all rotations and positions
    gsap.set([
      this.refs.group.current.rotation,
      this.refs.head.current.rotation,
      this.refs.leftArm.current.rotation,
      this.refs.rightArm.current.rotation,
      this.refs.leftLeg.current.rotation,
      this.refs.rightLeg.current.rotation
    ], { x: 0, y: 0, z: 0 });

    gsap.set(this.refs.group.current.position, { x: 0, y: 0, z: 0 });
    gsap.set(this.refs.leftArm.current.position, { z: 0 });

    this.isAnimating = false;
  }
}

.robot-controls {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  color: white;
  font-family: 'Arial', sans-serif;
  min-width: 280px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.robot-controls h3 {
  margin: 0 0 15px 0;
  text-align: center;
  color: #4A90E2;
  font-size: 1.2em;
  text-shadow: 0 0 10px rgba(74, 144, 226, 0.5);
}

.animation-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  font-size: 0.9em;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-indicator.animating {
  color: #FF6B35;
  animation: pulse 1.5s infinite;
}

.status-indicator.idle {
  color: #00FF88;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.current-animation {
  font-size: 0.8em;
  color: #ccc;
  text-transform: capitalize;
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-bottom: 15px;
}

.control-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  padding: 12px 8px;
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85em;
  min-height: 70px;
}

.control-button:hover:not(:disabled) {
  background: linear-gradient(145deg, #3a3a3a, #2a2a2a);
  border-color: #4A90E2;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.control-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
}

.control-button.active {
  background: linear-gradient(145deg, #4A90E2, #357ABD);
  border-color: #4A90E2;
  box-shadow: 0 0 15px rgba(74, 144, 226, 0.4);
}

.control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.control-button .emoji {
  font-size: 1.5em;
  line-height: 1;
}

.control-button .label {
  font-size: 0.75em;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
}

.instructions {
  font-size: 0.8em;
  color: #aaa;
  line-height: 1.4;
}

.instructions p {
  margin: 5px 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .robot-controls {
    position: relative;
    top: auto;
    left: auto;
    margin: 20px auto;
    max-width: 300px;
  }
  
  .controls-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .control-button {
    min-height: 60px;
    padding: 8px 4px;
  }
  
  .control-button .emoji {
    font-size: 1.2em;
  }
  
  .control-button .label {
    font-size: 0.7em;
  }
}

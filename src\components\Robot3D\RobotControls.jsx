import React from 'react';
import './RobotControls.css';

export function RobotControls({ robotRef, currentAnimation, isAnimating }) {
  const handleAnimation = (animationType) => {
    console.log('Button clicked:', animationType, 'Robot ref:', !!robotRef.current, 'Is animating:', isAnimating);

    if (!robotRef.current) {
      console.error('Robot ref not available');
      return;
    }

    if (isAnimating && animationType !== 'reset') {
      console.warn('Animation already in progress, ignoring click');
      return;
    }

    try {
      robotRef.current.playAnimation(animationType);
    } catch (error) {
      console.error('Error calling playAnimation:', error);
    }
  };

  const animations = [
    { key: 'wave', label: '👋 Wave', emoji: '👋' },
    { key: 'jump', label: '🦘 Jump', emoji: '🦘' },
    { key: 'punch', label: '👊 Punch', emoji: '👊' },
    { key: 'nodYes', label: '✅ Nod Yes', emoji: '✅' },
    { key: 'shakeNo', label: '❌ Shake No', emoji: '❌' },
    { key: 'dance', label: '💃 Dance', emoji: '💃' },
    { key: 'reset', label: '🔄 Reset', emoji: '🔄' }
  ];

  return (
    <div className="robot-controls">
      <h3>Robot Controls</h3>
      <div className="animation-status">
        <span className={`status-indicator ${isAnimating ? 'animating' : 'idle'}`}>
          {isAnimating ? '🤖 Animating...' : '😴 Idle'}
        </span>
        <span className="current-animation">
          Current: {currentAnimation}
        </span>
      </div>

      <div className="controls-grid">
        {animations.map(({ key, label, emoji }) => (
          <button
            key={key}
            className={`control-button ${currentAnimation === key ? 'active' : ''}`}
            onClick={() => handleAnimation(key)}
            disabled={isAnimating && key !== 'reset'}
            title={label}
          >
            <span className="emoji">{emoji}</span>
            <span className="label">{label.split(' ').slice(1).join(' ')}</span>
          </button>
        ))}
      </div>

      <div className="instructions">
        <p>Click the buttons above to make the robot perform different animations!</p>
        <p>The robot will return to idle state after each animation completes.</p>
      </div>
    </div>
  );
}

export default RobotControls;

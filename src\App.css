.app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  color: white;
  font-family: 'Arial', sans-serif;
  overflow: hidden;
}

.app-header {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 100;
  text-align: right;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  padding: 20px;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.app-header h1 {
  margin: 0 0 10px 0;
  font-size: 1.8em;
  color: #4A90E2;
  text-shadow: 0 0 10px rgba(74, 144, 226, 0.5);
}

.app-header p {
  margin: 0 0 15px 0;
  color: #ccc;
  font-size: 0.9em;
}

.random-button {
  background: linear-gradient(145deg, #FF6B35, #E55A2B);
  border: none;
  border-radius: 10px;
  padding: 10px 20px;
  color: white;
  font-size: 0.9em;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.random-button:hover:not(:disabled) {
  background: linear-gradient(145deg, #E55A2B, #CC4A1F);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.random-button:active:not(:disabled) {
  transform: translateY(0);
}

.random-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.scene-container {
  position: relative;
  width: 100vw;
  height: 100vh;
}

/* Responsive design */
@media (max-width: 768px) {
  .app-header {
    position: relative;
    top: auto;
    right: auto;
    margin: 10px;
    text-align: center;
  }

  .app-header h1 {
    font-size: 1.4em;
  }

  .scene-container {
    height: calc(100vh - 120px);
  }
}

/* Loading and error states */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.2em;
  color: #4A90E2;
}

.error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.2em;
  color: #FF6B35;
  text-align: center;
  padding: 20px;
}

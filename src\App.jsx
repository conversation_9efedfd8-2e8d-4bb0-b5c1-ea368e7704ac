import React from 'react';
import Scene3D from './components/Scene3D';
import Robot3D from './components/Robot3D/Robot3D';
import RobotControls from './components/Robot3D/RobotControls';
import { useRobotAnimations } from './hooks/useRobotAnimations';
import './App.css';

function App() {
  const {
    robotRef,
    currentAnimation,
    isAnimating,
    handleAnimationChange,
    playRandomAnimation
  } = useRobotAnimations();

  return (
    <div className="app">
      <header className="app-header">
        <h1>🤖 Cute 3D Robot</h1>
        <p>Interactive 3D Robot with GSAP Animations</p>
        <button
          className="random-button"
          onClick={playRandomAnimation}
          disabled={isAnimating}
        >
          🎲 Random Animation
        </button>
      </header>

      <div className="scene-container">
        <Scene3D>
          <Robot3D
            ref={robotRef}
            position={[0, 0, 0]}
            onAnimationChange={handleAnimationChange}
          />
        </Scene3D>

        <RobotControls
          robotRef={robotRef}
          currentAnimation={currentAnimation}
          isAnimating={isAnimating}
        />
      </div>
    </div>
  );
}

export default App;

import React, { Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment, ContactShadows, Text } from '@react-three/drei';
import * as THREE from 'three';

function SceneLighting() {
  return (
    <>
      {/* Ambient light for overall illumination */}
      <ambientLight intensity={0.4} />
      
      {/* Main directional light */}
      <directionalLight
        position={[10, 10, 5]}
        intensity={1}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={50}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
      />
      
      {/* Fill light */}
      <directionalLight
        position={[-5, 5, -5]}
        intensity={0.3}
        color="#4A90E2"
      />
      
      {/* Rim light */}
      <directionalLight
        position={[0, 5, -10]}
        intensity={0.5}
        color="#FF6B35"
      />
    </>
  );
}

function SceneEnvironment() {
  return (
    <>
      {/* Ground plane */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -3, 0]} receiveShadow>
        <planeGeometry args={[20, 20]} />
        <meshStandardMaterial 
          color="#1a1a1a" 
          metalness={0.1}
          roughness={0.8}
        />
      </mesh>
      
      {/* Contact shadows for better grounding */}
      <ContactShadows
        position={[0, -2.99, 0]}
        opacity={0.4}
        scale={10}
        blur={2}
        far={4}
      />
      
      {/* Background elements */}
      <mesh position={[0, 0, -8]}>
        <planeGeometry args={[20, 12]} />
        <meshStandardMaterial 
          color="#0a0a0a"
          metalness={0.2}
          roughness={0.9}
        />
      </mesh>
      
      {/* Decorative elements */}
      <mesh position={[-6, 2, -7]} rotation={[0, 0, Math.PI / 4]}>
        <boxGeometry args={[0.5, 0.5, 0.1]} />
        <meshStandardMaterial 
          color="#4A90E2"
          emissive="#4A90E2"
          emissiveIntensity={0.2}
        />
      </mesh>
      
      <mesh position={[6, 1, -7]} rotation={[0, 0, -Math.PI / 6]}>
        <cylinderGeometry args={[0.3, 0.3, 0.1]} />
        <meshStandardMaterial 
          color="#FF6B35"
          emissive="#FF6B35"
          emissiveIntensity={0.2}
        />
      </mesh>
    </>
  );
}

function LoadingFallback() {
  return (
    <Text
      position={[0, 0, 0]}
      fontSize={1}
      color="#4A90E2"
      anchorX="center"
      anchorY="middle"
    >
      Loading Robot...
    </Text>
  );
}

export function Scene3D({ children, ...props }) {
  return (
    <Canvas
      shadows
      camera={{ 
        position: [5, 3, 5], 
        fov: 50,
        near: 0.1,
        far: 100
      }}
      gl={{ 
        antialias: true,
        toneMapping: THREE.ACESFilmicToneMapping,
        toneMappingExposure: 1.2
      }}
      {...props}
    >
      {/* Scene lighting */}
      <SceneLighting />
      
      {/* Environment */}
      <Environment preset="warehouse" />
      
      {/* Scene environment */}
      <SceneEnvironment />
      
      {/* Robot and other content */}
      <Suspense fallback={<LoadingFallback />}>
        {children}
      </Suspense>
      
      {/* Camera controls */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        minDistance={3}
        maxDistance={15}
        minPolarAngle={0}
        maxPolarAngle={Math.PI / 2}
        target={[0, 0, 0]}
      />
    </Canvas>
  );
}

export default Scene3D;

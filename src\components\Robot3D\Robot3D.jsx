import React, { useRef, useEffect, useState, forwardRef } from 'react';
import { RobotGeometry } from './RobotGeometry';
import { RobotAnimations } from './RobotAnimations';

export const Robot3D = forwardRef(({ onAnimationChange, ...props }, ref) => {
  const animationRefs = useRef();
  const [animations, setAnimations] = useState(null);
  const [currentAnimation, setCurrentAnimation] = useState('idle');

  // Initialize animations when refs are ready
  useEffect(() => {
    const timer = setTimeout(() => {
      if (animationRefs.current) {
        console.log('Initializing animations with refs:', animationRefs.current);
        const robotAnimations = new RobotAnimations(animationRefs);
        setAnimations(robotAnimations);
      }
    }, 100); // Small delay to ensure refs are set

    return () => clearTimeout(timer);
  }, []);

  // Animation methods
  const playAnimation = (animationType) => {
    console.log('Playing animation:', animationType, 'Animations available:', !!animations);

    if (!animations) {
      console.warn('Animations not initialized yet');
      return;
    }

    if (animations.isAnimating) {
      console.warn('Animation already in progress');
      return;
    }

    setCurrentAnimation(animationType);
    if (onAnimationChange) onAnimationChange(animationType);

    try {
      switch (animationType) {
        case 'wave':
          animations.wave();
          break;
        case 'jump':
          animations.jump();
          break;
        case 'punch':
          animations.punch();
          break;
        case 'nodYes':
          animations.nodYes();
          break;
        case 'shakeNo':
          animations.shakeNo();
          break;
        case 'dance':
          animations.dance();
          break;
        case 'reset':
          animations.reset();
          setCurrentAnimation('idle');
          break;
        default:
          console.warn('Unknown animation type:', animationType);
          break;
      }
    } catch (error) {
      console.error('Error playing animation:', error);
    }
  };

  // Expose animation methods to parent
  React.useImperativeHandle(ref, () => ({
    playAnimation,
    getCurrentAnimation: () => currentAnimation,
    isAnimating: () => animations?.isAnimating || false
  }));

  return (
    <RobotGeometry
      animationRefs={animationRefs}
      {...props}
    />
  );
});

Robot3D.displayName = 'Robot3D';

export default Robot3D;

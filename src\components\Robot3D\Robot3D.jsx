import React, { useRef, useEffect, useState } from 'react';
import { RobotGeometry } from './RobotGeometry';
import { RobotAnimations } from './RobotAnimations';

export function Robot3D({ onAnimationChange, ...props }) {
  const animationRefs = useRef();
  const [animations, setAnimations] = useState(null);
  const [currentAnimation, setCurrentAnimation] = useState('idle');

  // Initialize animations when refs are ready
  useEffect(() => {
    if (animationRefs.current) {
      const robotAnimations = new RobotAnimations(animationRefs);
      setAnimations(robotAnimations);
    }
  }, []);

  // Animation methods
  const playAnimation = (animationType) => {
    if (!animations || animations.isAnimating) return;
    
    setCurrentAnimation(animationType);
    if (onAnimationChange) onAnimationChange(animationType);

    switch (animationType) {
      case 'wave':
        animations.wave();
        break;
      case 'jump':
        animations.jump();
        break;
      case 'punch':
        animations.punch();
        break;
      case 'nodYes':
        animations.nodYes();
        break;
      case 'shakeNo':
        animations.shakeNo();
        break;
      case 'dance':
        animations.dance();
        break;
      case 'reset':
        animations.reset();
        setCurrentAnimation('idle');
        break;
      default:
        break;
    }
  };

  // Expose animation methods to parent
  React.useImperativeHandle(props.ref, () => ({
    playAnimation,
    getCurrentAnimation: () => currentAnimation,
    isAnimating: () => animations?.isAnimating || false
  }));

  return (
    <RobotGeometry 
      animationRefs={animationRefs}
      {...props}
    />
  );
}

export default Robot3D;
